[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "erp-py"
version = "1.0.0"
description = "ERP System in Python"
authors = [
    {name = "ERP Team"}
]
requires-python = ">=3.8"
dependencies = [
    "fastapi>=0.115.0",
    "uvicorn[standard]>=0.32.0",
    "asyncpg>=0.30.0",
    "python-dateutil>=2.9.0",
    "psutil>=5.9.0",
    "watchdog>=3.0.0",
]

[project.optional-dependencies]
test = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.12.0",
]
lint = [
    "ruff>=0.8.0",
    "flake8>=7.0.0",
    "pylint>=3.0.0",
]
dev = [
    "erp-py[test]",
    "erp-py[lint]",
]

[tool.ruff]
# Recommended ruff settings for a modern Python project
line-length = 88
indent-width = 4

# Assume Python 3.8
target-version = "py38"

[tool.ruff.lint]
# Enable pycodestyle (`E`) and Pyflakes (`F`) codes by default.
select = [
    "E",     # pycodestyle errors
    "W",     # pycodestyle warnings
    "F",     # pyflakes
    "I",     # isort
    "C",     # mccabe
    "B",     # bugbear
    "Q",     # flake8-quotes
    "N",     # pep8-naming
    "UP",    # pyupgrade
    "YTT",   # flake8-2020
    "ANN",   # flake8-annotations
    "S",     # bandit (security)
    "BLE",   # flake8-blind-except
    "FBT",   # flake8-boolean-trap
    "A",     # flake8-builtins
    "C4",    # flake8-comprehensions
    "DTZ",   # flake8-datetimez
    "T10",   # flake8-debugger
    "EXE",   # flake8-executable
    "ISC",   # flake8-implicit-str-concat
    "G",     # flake8-logging-format
    "PIE",   # flake8-pie
    "PYI",   # flake8-pyi
    "RUF",   # ruff-specific rules
    "SLF",   # flake8-self
    "TID",   # flake8-tidy-imports
    "TCH",   # flake8-type-checking
    "ARG",   # flake8-unused-arguments
    "PTH",   # flake8-use-pathlib
    "TD",    # flake8-todos
    "FIX",   # flake8-fixme
    "ERA",   # eradicate
    "PL",    # pylint
    "TRY",   # tryceratops
    "FLY",   # flynt
    "PERF",  # perflint
    "FURB",  # refurb
    "LOG",   # flake8-logging
]

# Never enforce `E501` (line length violations) - handled by formatter
extend-ignore = [
    "E501",   # Line too long
    "ANN101", # Missing type annotation for self
    "ANN102", # Missing type annotation for cls
    "ANN401", # Dynamically typed expressions (typing.Any) are disallowed
]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.lint.per-file-ignores]
# Ignore import violations in __init__.py files
"__init__.py" = ["F401", "E402", "F403", "F405"]
# Ignore security issues in test files
"tests/**/*" = ["S101", "S106", "S311", "S105", "S107"]
# Ignore complexity in test files
"tests/**/*" = ["C901", "PLR0913", "PLR0915"]

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

[tool.flake8]
max-line-length = 88
ignore = [
    "E203",  # Whitespace before ':'
    "W503",  # Line break before binary operator
    "E501",  # Line too long (handled by ruff)
]
exclude = [
    ".git",
    "__pycache__",
    "venv",
    ".venv",
    "build",
    "dist",
    "*.egg-info",
]
per-file-ignores = [
    "__init__.py:F401,E402,F403,F405",
    "tests/**:S101,S106,S311,S105,S107",
]

[tool.pylint.master]
# Use multiple processes to speed up Pylint
jobs = 0

[tool.pylint.messages_control]
# Disable the message, report, category or checker with the given id(s).
disable = [
    "missing-docstring",
    "invalid-name",
    "too-few-public-methods",
    "too-many-arguments",
    "too-many-locals",
    "too-many-branches",
    "too-many-statements",
    "too-many-instance-attributes",
    "too-many-public-methods",
    "too-many-return-statements",
    "too-many-nested-blocks",
    "too-many-boolean-expressions",
    "line-too-long",  # Handled by ruff
    "import-error",   # Handled by ruff
    "unused-import",  # Handled by ruff
    "no-name-in-module",  # Handled by ruff
    "no-member",      # Handled by ruff
    "redefined-outer-name",  # Handled by ruff
    "unused-variable",  # Handled by ruff
    "too-many-positional-arguments",  # Handled by ruff
]

[tool.pylint.format]
# Maximum number of characters on a single line
max-line-length = 88

[tool.pylint.design]
# Maximum number of arguments for function / method
max-args = 10

# Maximum number of locals for function / method body
max-locals = 20

# Maximum number of statements in function / method body
max-statements = 50

# Maximum number of branches in function / method body
max-branches = 20

# Maximum number of return / yield for function / method body
max-returns = 6

[tool.pylint.similarities]
# Minimum lines number of a similarity.
min-similarity-lines = 10

# Ignore comments when computing similarities.
ignore-comments = true

# Ignore docstrings when computing similarities.
ignore-docstrings = true

# Ignore imports when computing similarities.
ignore-imports = true