"""
Data Validation Utilities for ERP system

Provides comprehensive validation for data records, fields, and models
with configurable rules and detailed error reporting.
"""

import re
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from ..exceptions import DataLoadingError
from ...logging import get_logger


class ValidationLevel(Enum):
    """Validation severity levels"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


@dataclass
class ValidationIssue:
    """Individual validation issue"""
    level: ValidationLevel
    message: str
    field_name: Optional[str] = None
    value: Any = None
    rule_name: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ValidationResult:
    """Result of validation operation"""
    valid: bool = True
    issues: List[ValidationIssue] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_error(self, message: str, field_name: str = None, value: Any = None, rule_name: str = None):
        """Add an error issue"""
        self.issues.append(ValidationIssue(
            level=ValidationLevel.ERROR,
            message=message,
            field_name=field_name,
            value=value,
            rule_name=rule_name
        ))
        self.valid = False
    
    def add_warning(self, message: str, field_name: str = None, value: Any = None, rule_name: str = None):
        """Add a warning issue"""
        self.issues.append(ValidationIssue(
            level=ValidationLevel.WARNING,
            message=message,
            field_name=field_name,
            value=value,
            rule_name=rule_name
        ))
    
    def add_info(self, message: str, field_name: str = None, value: Any = None, rule_name: str = None):
        """Add an info issue"""
        self.issues.append(ValidationIssue(
            level=ValidationLevel.INFO,
            message=message,
            field_name=field_name,
            value=value,
            rule_name=rule_name
        ))
    
    @property
    def errors(self) -> List[ValidationIssue]:
        """Get error issues"""
        return [issue for issue in self.issues if issue.level == ValidationLevel.ERROR]
    
    @property
    def warnings(self) -> List[ValidationIssue]:
        """Get warning issues"""
        return [issue for issue in self.issues if issue.level == ValidationLevel.WARNING]
    
    @property
    def error_count(self) -> int:
        """Get error count"""
        return len(self.errors)
    
    @property
    def warning_count(self) -> int:
        """Get warning count"""
        return len(self.warnings)


class ValidationError(DataLoadingError):
    """Exception raised when validation fails"""
    
    def __init__(self, message: str, validation_result: ValidationResult = None):
        super().__init__(message)
        self.validation_result = validation_result


class ValidationRule:
    """Base class for validation rules"""
    
    def __init__(self, name: str, level: ValidationLevel = ValidationLevel.ERROR):
        self.name = name
        self.level = level
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        """Validate a value"""
        raise NotImplementedError


class RequiredRule(ValidationRule):
    """Rule to check if value is required"""
    
    def __init__(self):
        super().__init__("required", ValidationLevel.ERROR)
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        result = ValidationResult()
        if value is None or (isinstance(value, str) and not value.strip()):
            result.add_error("Field is required", rule_name=self.name)
        return result


class TypeRule(ValidationRule):
    """Rule to check value type"""
    
    def __init__(self, expected_type: type, allow_none: bool = True):
        super().__init__(f"type_{expected_type.__name__}", ValidationLevel.ERROR)
        self.expected_type = expected_type
        self.allow_none = allow_none
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        result = ValidationResult()
        if value is None and self.allow_none:
            return result
        
        if not isinstance(value, self.expected_type):
            result.add_error(
                f"Expected {self.expected_type.__name__}, got {type(value).__name__}",
                value=value,
                rule_name=self.name
            )
        return result


class LengthRule(ValidationRule):
    """Rule to check string length"""
    
    def __init__(self, min_length: int = None, max_length: int = None):
        super().__init__("length", ValidationLevel.ERROR)
        self.min_length = min_length
        self.max_length = max_length
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        result = ValidationResult()
        if value is None:
            return result
        
        if not isinstance(value, str):
            result.add_error("Length validation only applies to strings", rule_name=self.name)
            return result
        
        length = len(value)
        if self.min_length is not None and length < self.min_length:
            result.add_error(
                f"String too short: {length} < {self.min_length}",
                value=value,
                rule_name=self.name
            )
        
        if self.max_length is not None and length > self.max_length:
            result.add_error(
                f"String too long: {length} > {self.max_length}",
                value=value,
                rule_name=self.name
            )
        
        return result


class PatternRule(ValidationRule):
    """Rule to check string pattern"""
    
    def __init__(self, pattern: str, flags: int = 0):
        super().__init__("pattern", ValidationLevel.ERROR)
        self.pattern = pattern
        self.regex = re.compile(pattern, flags)
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        result = ValidationResult()
        if value is None:
            return result
        
        if not isinstance(value, str):
            result.add_error("Pattern validation only applies to strings", rule_name=self.name)
            return result
        
        if not self.regex.match(value):
            result.add_error(
                f"String does not match pattern: {self.pattern}",
                value=value,
                rule_name=self.name
            )
        
        return result


class RangeRule(ValidationRule):
    """Rule to check numeric range"""
    
    def __init__(self, min_value: Union[int, float] = None, max_value: Union[int, float] = None):
        super().__init__("range", ValidationLevel.ERROR)
        self.min_value = min_value
        self.max_value = max_value
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        result = ValidationResult()
        if value is None:
            return result
        
        if not isinstance(value, (int, float)):
            result.add_error("Range validation only applies to numbers", rule_name=self.name)
            return result
        
        if self.min_value is not None and value < self.min_value:
            result.add_error(
                f"Value too small: {value} < {self.min_value}",
                value=value,
                rule_name=self.name
            )
        
        if self.max_value is not None and value > self.max_value:
            result.add_error(
                f"Value too large: {value} > {self.max_value}",
                value=value,
                rule_name=self.name
            )
        
        return result


class ChoiceRule(ValidationRule):
    """Rule to check if value is in allowed choices"""
    
    def __init__(self, choices: List[Any]):
        super().__init__("choice", ValidationLevel.ERROR)
        self.choices = choices
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        result = ValidationResult()
        if value is None:
            return result
        
        if value not in self.choices:
            result.add_error(
                f"Value not in allowed choices: {value} not in {self.choices}",
                value=value,
                rule_name=self.name
            )
        
        return result


class FieldValidator:
    """Validator for individual fields"""
    
    def __init__(self, field_name: str):
        self.field_name = field_name
        self.rules: List[ValidationRule] = []
        self.custom_validators: List[Callable] = []
        self.logger = get_logger(__name__)
    
    def add_rule(self, rule: ValidationRule):
        """Add a validation rule"""
        self.rules.append(rule)
        return self
    
    def required(self):
        """Add required rule"""
        return self.add_rule(RequiredRule())
    
    def type_check(self, expected_type: type, allow_none: bool = True):
        """Add type check rule"""
        return self.add_rule(TypeRule(expected_type, allow_none))
    
    def length(self, min_length: int = None, max_length: int = None):
        """Add length rule"""
        return self.add_rule(LengthRule(min_length, max_length))
    
    def pattern(self, pattern: str, flags: int = 0):
        """Add pattern rule"""
        return self.add_rule(PatternRule(pattern, flags))
    
    def range_check(self, min_value: Union[int, float] = None, max_value: Union[int, float] = None):
        """Add range rule"""
        return self.add_rule(RangeRule(min_value, max_value))
    
    def choices(self, choices: List[Any]):
        """Add choice rule"""
        return self.add_rule(ChoiceRule(choices))
    
    def custom(self, validator: Callable):
        """Add custom validator function"""
        self.custom_validators.append(validator)
        return self
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        """Validate field value"""
        result = ValidationResult()
        context = context or {}
        
        # Apply built-in rules
        for rule in self.rules:
            rule_result = rule.validate(value, context)
            result.issues.extend(rule_result.issues)
            if not rule_result.valid:
                result.valid = False
        
        # Apply custom validators
        for validator in self.custom_validators:
            try:
                custom_result = validator(value, context)
                if isinstance(custom_result, ValidationResult):
                    result.issues.extend(custom_result.issues)
                    if not custom_result.valid:
                        result.valid = False
                elif isinstance(custom_result, bool):
                    if not custom_result:
                        result.add_error(f"Custom validation failed for field {self.field_name}")
                elif isinstance(custom_result, str):
                    result.add_error(custom_result)
            except Exception as e:
                result.add_error(f"Custom validator error: {e}")
        
        return result


class ModelValidator:
    """Validator for model records"""
    
    def __init__(self, model_name: str):
        self.model_name = model_name
        self.field_validators: Dict[str, FieldValidator] = {}
        self.record_validators: List[Callable] = []
        self.logger = get_logger(__name__)
    
    def field(self, field_name: str) -> FieldValidator:
        """Get or create field validator"""
        if field_name not in self.field_validators:
            self.field_validators[field_name] = FieldValidator(field_name)
        return self.field_validators[field_name]
    
    def add_record_validator(self, validator: Callable):
        """Add record-level validator"""
        self.record_validators.append(validator)
        return self
    
    def validate_record(self, record: Dict[str, Any], context: Dict[str, Any] = None) -> ValidationResult:
        """Validate a complete record"""
        result = ValidationResult()
        context = context or {}
        context['model'] = self.model_name
        
        # Validate individual fields
        values = record.get('values', {})
        for field_name, field_value in values.items():
            if field_name in self.field_validators:
                field_result = self.field_validators[field_name].validate(field_value, context)
                # Add field name to issues
                for issue in field_result.issues:
                    issue.field_name = field_name
                result.issues.extend(field_result.issues)
                if not field_result.valid:
                    result.valid = False
        
        # Apply record-level validators
        for validator in self.record_validators:
            try:
                record_result = validator(record, context)
                if isinstance(record_result, ValidationResult):
                    result.issues.extend(record_result.issues)
                    if not record_result.valid:
                        result.valid = False
                elif isinstance(record_result, bool):
                    if not record_result:
                        result.add_error(f"Record validation failed for {self.model_name}")
                elif isinstance(record_result, str):
                    result.add_error(record_result)
            except Exception as e:
                result.add_error(f"Record validator error: {e}")
        
        return result


class DataValidator:
    """Main data validator"""
    
    def __init__(self):
        self.model_validators: Dict[str, ModelValidator] = {}
        self.global_validators: List[Callable] = []
        self.logger = get_logger(__name__)
    
    def model(self, model_name: str) -> ModelValidator:
        """Get or create model validator"""
        if model_name not in self.model_validators:
            self.model_validators[model_name] = ModelValidator(model_name)
        return self.model_validators[model_name]
    
    def add_global_validator(self, validator: Callable):
        """Add global validator"""
        self.global_validators.append(validator)
        return self
    
    def validate_data(self, data: List[Dict[str, Any]], context: Dict[str, Any] = None) -> ValidationResult:
        """Validate a list of data records"""
        result = ValidationResult()
        context = context or {}
        
        for i, record in enumerate(data):
            try:
                # Validate record structure
                if not isinstance(record, dict):
                    result.add_error(f"Record {i} is not a dictionary")
                    continue
                
                model_name = record.get('model')
                if not model_name:
                    result.add_error(f"Record {i} missing model name")
                    continue
                
                # Apply model-specific validation
                if model_name in self.model_validators:
                    record_result = self.model_validators[model_name].validate_record(record, context)
                    result.issues.extend(record_result.issues)
                    if not record_result.valid:
                        result.valid = False
                
                # Apply global validators
                for validator in self.global_validators:
                    try:
                        global_result = validator(record, context)
                        if isinstance(global_result, ValidationResult):
                            result.issues.extend(global_result.issues)
                            if not global_result.valid:
                                result.valid = False
                        elif isinstance(global_result, bool):
                            if not global_result:
                                result.add_error(f"Global validation failed for record {i}")
                        elif isinstance(global_result, str):
                            result.add_error(global_result)
                    except Exception as e:
                        result.add_error(f"Global validator error for record {i}: {e}")
                        
            except Exception as e:
                result.add_error(f"Error validating record {i}: {e}")
        
        return result
