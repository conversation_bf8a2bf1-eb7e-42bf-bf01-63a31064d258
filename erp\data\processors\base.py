"""
Base Data Processor for ERP system

Provides the foundation for all specialized data processors with common functionality,
error handling, and processing patterns.
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from ..exceptions import DataLoadingError
from ..xmlid_manager import XMLIDManager
from ...logging import get_logger
from ...database.connection.manager import DatabaseManager


class ProcessorStatus(Enum):
    """Processor execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ProcessorPriority(Enum):
    """Processor priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class ProcessorResult:
    """Result of data processor execution"""
    processor_name: str
    status: ProcessorStatus
    processed_count: int = 0
    success_count: int = 0
    error_count: int = 0
    warning_count: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_error(self, error: str):
        """Add an error message"""
        self.errors.append(error)
        self.error_count += 1
    
    def add_warning(self, warning: str):
        """Add a warning message"""
        self.warnings.append(warning)
        self.warning_count += 1
    
    def mark_success(self):
        """Mark an item as successfully processed"""
        self.success_count += 1
        self.processed_count += 1
    
    def mark_processed(self):
        """Mark an item as processed (without success/failure)"""
        self.processed_count += 1
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.processed_count == 0:
            return 0.0
        return (self.success_count / self.processed_count) * 100


class ProcessorError(DataLoadingError):
    """Exception raised by data processors"""
    
    def __init__(self, message: str, processor_name: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.processor_name = processor_name
        self.details = details or {}


class BaseDataProcessor(ABC):
    """
    Base class for all data processors
    
    Provides common functionality for processing different types of data records
    with proper error handling, logging, and result tracking.
    """
    
    def __init__(self, db_manager: DatabaseManager, name: str = None):
        """
        Initialize base data processor
        
        Args:
            db_manager: Database manager instance
            name: Processor name (defaults to class name)
        """
        self.db_manager = db_manager
        self.name = name or self.__class__.__name__
        self.logger = get_logger(f"{__name__}.{self.name}")

        # Common components for field processing
        self.xmlid_manager = XMLIDManager(db_manager)

        # Processing state
        self.status = ProcessorStatus.PENDING
        self.priority = ProcessorPriority.NORMAL
        self.context = {}
        self.hooks = {
            'before_process': [],
            'after_process': [],
            'before_item': [],
            'after_item': [],
            'on_error': [],
            'on_success': []
        }

        # Statistics
        self.result = ProcessorResult(processor_name=self.name, status=ProcessorStatus.PENDING)
    
    def set_priority(self, priority: ProcessorPriority):
        """Set processor priority"""
        self.priority = priority
    
    def set_context(self, context: Dict[str, Any]):
        """Set processing context"""
        self.context.update(context)
    
    def add_hook(self, hook_type: str, callback: Callable):
        """Add a processing hook"""
        if hook_type in self.hooks:
            self.hooks[hook_type].append(callback)
    
    async def process(self, data: List[Dict[str, Any]], **kwargs) -> ProcessorResult:
        """
        Process a list of data items
        
        Args:
            data: List of data items to process
            **kwargs: Additional processing options
            
        Returns:
            ProcessorResult with processing statistics and results
        """
        self.result.start_time = datetime.utcnow()
        self.status = ProcessorStatus.RUNNING
        self.result.status = ProcessorStatus.RUNNING
        
        try:
            self.logger.info(f"Starting {self.name} processor with {len(data)} items")
            
            # Execute before_process hooks
            await self._execute_hooks('before_process', data=data, **kwargs)
            
            # Validate input data
            await self._validate_input(data)
            
            # Process items
            for item in data:
                try:
                    # Execute before_item hooks
                    await self._execute_hooks('before_item', item=item)
                    
                    # Process individual item
                    success = await self._process_item(item, **kwargs)
                    
                    if success:
                        self.result.mark_success()
                        await self._execute_hooks('on_success', item=item)
                    else:
                        self.result.mark_processed()
                    
                    # Execute after_item hooks
                    await self._execute_hooks('after_item', item=item, success=success)
                    
                except Exception as e:
                    error_msg = f"Error processing item {item.get('xml_id', 'unknown')}: {e}"
                    self.result.add_error(error_msg)
                    self.logger.error(error_msg)
                    await self._execute_hooks('on_error', item=item, error=e)
            
            # Execute after_process hooks
            await self._execute_hooks('after_process', result=self.result)
            
            self.status = ProcessorStatus.COMPLETED
            self.result.status = ProcessorStatus.COMPLETED
            
        except Exception as e:
            self.status = ProcessorStatus.FAILED
            self.result.status = ProcessorStatus.FAILED
            error_msg = f"Processor {self.name} failed: {e}"
            self.result.add_error(error_msg)
            self.logger.error(error_msg)
            raise ProcessorError(error_msg, self.name, {'original_error': str(e)})
        
        finally:
            self.result.end_time = datetime.utcnow()
            if self.result.start_time:
                self.result.duration = (self.result.end_time - self.result.start_time).total_seconds()
            
            self.logger.info(
                f"Processor {self.name} completed: "
                f"{self.result.success_count}/{self.result.processed_count} successful, "
                f"{self.result.error_count} errors, {self.result.warning_count} warnings"
            )
        
        return self.result
    
    async def _execute_hooks(self, hook_type: str, **kwargs):
        """Execute hooks of specified type"""
        for hook in self.hooks.get(hook_type, []):
            try:
                if asyncio.iscoroutinefunction(hook):
                    await hook(self, **kwargs)
                else:
                    hook(self, **kwargs)
            except Exception as e:
                self.logger.warning(f"Hook {hook_type} failed: {e}")
    
    async def _validate_input(self, data: List[Dict[str, Any]]):
        """Validate input data before processing"""
        if not isinstance(data, list):
            raise ProcessorError("Input data must be a list", self.name)
        
        for i, item in enumerate(data):
            if not isinstance(item, dict):
                raise ProcessorError(f"Item {i} must be a dictionary", self.name)
    
    @abstractmethod
    async def _process_item(self, item: Dict[str, Any], **kwargs) -> bool:
        """
        Process a single data item
        
        Args:
            item: Data item to process
            **kwargs: Additional processing options
            
        Returns:
            True if processing was successful, False otherwise
        """
        pass
    
    @abstractmethod
    def can_process(self, item: Dict[str, Any]) -> bool:
        """
        Check if this processor can handle the given item
        
        Args:
            item: Data item to check
            
        Returns:
            True if processor can handle this item
        """
        pass
    
    def get_supported_models(self) -> List[str]:
        """Get list of models this processor supports"""
        return []
    
    def get_processing_order(self) -> int:
        """Get processing order (lower numbers processed first)"""
        return 100

    # Common field processing methods
    async def _process_standard_field(self, field_def: Any) -> Any:
        """Process standard field values - common implementation for all processors"""
        if isinstance(field_def, dict):
            field_type = field_def.get('type')
            field_value = field_def.get('value')

            if field_type == 'ref':
                return await self._resolve_reference(field_value)
            elif field_type == 'eval':
                return self._evaluate_expression(field_value)
            else:
                return field_value
        else:
            return field_def

    async def _resolve_reference(self, ref_value: str) -> Optional[str]:
        """Resolve a reference to another record - common implementation"""
        try:
            return await self.xmlid_manager.resolve_xmlid_to_record_id(ref_value)
        except Exception as e:
            self.result.add_warning(f"Failed to resolve reference {ref_value}: {e}")
            return None

    def _evaluate_expression(self, expression: str) -> Any:
        """Safely evaluate a Python expression - common implementation"""
        try:
            # Handle common cases
            if expression == 'True':
                return True
            elif expression == 'False':
                return False
            elif expression == 'None':
                return None
            elif expression.startswith("'") and expression.endswith("'"):
                return expression[1:-1]  # String literal
            elif expression.startswith('"') and expression.endswith('"'):
                return expression[1:-1]  # String literal
            else:
                # Try to evaluate as number
                try:
                    return int(expression)
                except ValueError:
                    try:
                        return float(expression)
                    except ValueError:
                        return expression  # Return as string
        except Exception:
            return expression

    def _convert_to_boolean(self, value: Any) -> bool:
        """Convert value to boolean - common implementation"""
        if isinstance(value, bool):
            return value
        elif isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        elif isinstance(value, (int, float)):
            return bool(value)
        else:
            return False

    def _evaluate_boolean_expression(self, expression: str) -> bool:
        """Evaluate boolean expression - common implementation"""
        try:
            if expression.lower() in ('true', '1'):
                return True
            elif expression.lower() in ('false', '0'):
                return False
            else:
                return False
        except Exception:
            return False
