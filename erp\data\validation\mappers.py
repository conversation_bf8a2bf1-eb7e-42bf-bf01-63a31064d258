"""
Data Mapping Utilities for ERP system

Provides field mapping, model mapping, and data structure mapping
capabilities for data loading operations.
"""

from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, field

from ..exceptions import DataLoadingError
from ...logging import get_logger


@dataclass
class MappingResult:
    """Result of mapping operation"""
    success: bool = True
    mapped_value: Any = None
    original_value: Any = None
    mapping_name: Optional[str] = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_error(self, message: str):
        """Add an error message"""
        self.errors.append(message)
        self.success = False
    
    def add_warning(self, message: str):
        """Add a warning message"""
        self.warnings.append(message)


class MappingError(DataLoadingError):
    """Exception raised when mapping fails"""
    
    def __init__(self, message: str, mapping_result: MappingResult = None):
        super().__init__(message)
        self.mapping_result = mapping_result


class FieldMapper:
    """Mapper for individual fields"""
    
    def __init__(self, source_field: str, target_field: str = None):
        self.source_field = source_field
        self.target_field = target_field or source_field
        self.value_mappings: Dict[Any, Any] = {}
        self.default_value = None
        self.transform_function: Optional[Callable] = None
        self.logger = get_logger(__name__)
    
    def add_value_mapping(self, source_value: Any, target_value: Any):
        """Add a value mapping"""
        self.value_mappings[source_value] = target_value
        return self
    
    def set_default_value(self, default_value: Any):
        """Set default value for unmapped values"""
        self.default_value = default_value
        return self
    
    def set_transform_function(self, transform_func: Callable):
        """Set transformation function"""
        self.transform_function = transform_func
        return self
    
    def map_value(self, value: Any, context: Dict[str, Any] = None) -> MappingResult:
        """Map a field value"""
        result = MappingResult(
            original_value=value,
            mapping_name=f"{self.source_field}->{self.target_field}"
        )
        
        try:
            mapped_value = value
            
            # Apply value mappings
            if value in self.value_mappings:
                mapped_value = self.value_mappings[value]
            elif self.default_value is not None:
                mapped_value = self.default_value
                result.add_warning(f"Used default value for unmapped value: {value}")
            
            # Apply transformation function
            if self.transform_function:
                try:
                    mapped_value = self.transform_function(mapped_value, context)
                except Exception as e:
                    result.add_error(f"Transformation function failed: {e}")
                    mapped_value = value
            
            result.mapped_value = mapped_value
            
        except Exception as e:
            result.add_error(f"Field mapping failed: {e}")
        
        return result


class ModelMapper:
    """Mapper for model records"""
    
    def __init__(self, source_model: str, target_model: str = None):
        self.source_model = source_model
        self.target_model = target_model or source_model
        self.field_mappers: Dict[str, FieldMapper] = {}
        self.excluded_fields: set = set()
        self.required_fields: set = set()
        self.record_transform_function: Optional[Callable] = None
        self.logger = get_logger(__name__)
    
    def add_field_mapping(self, source_field: str, target_field: str = None) -> FieldMapper:
        """Add field mapping"""
        mapper = FieldMapper(source_field, target_field)
        self.field_mappers[source_field] = mapper
        return mapper
    
    def exclude_field(self, field_name: str):
        """Exclude a field from mapping"""
        self.excluded_fields.add(field_name)
        return self
    
    def require_field(self, field_name: str):
        """Mark a field as required"""
        self.required_fields.add(field_name)
        return self
    
    def set_record_transform_function(self, transform_func: Callable):
        """Set record-level transformation function"""
        self.record_transform_function = transform_func
        return self
    
    def map_record(self, record: Dict[str, Any], context: Dict[str, Any] = None) -> MappingResult:
        """Map a complete record"""
        result = MappingResult(
            original_value=record,
            mapping_name=f"{self.source_model}->{self.target_model}"
        )
        
        try:
            # Start with original record structure
            mapped_record = record.copy()
            mapped_record['model'] = self.target_model
            
            # Map field values
            values = record.get('values', {})
            mapped_values = {}
            
            for field_name, field_value in values.items():
                # Skip excluded fields
                if field_name in self.excluded_fields:
                    continue
                
                # Apply field mapping if exists
                if field_name in self.field_mappers:
                    field_result = self.field_mappers[field_name].map_value(field_value, context)
                    if field_result.success:
                        target_field = self.field_mappers[field_name].target_field
                        mapped_values[target_field] = field_result.mapped_value
                    else:
                        mapped_values[field_name] = field_value
                        result.errors.extend(field_result.errors)
                        result.warnings.extend(field_result.warnings)
                else:
                    # No mapping, keep original
                    mapped_values[field_name] = field_value
            
            # Check required fields
            for required_field in self.required_fields:
                if required_field not in mapped_values:
                    result.add_error(f"Required field missing after mapping: {required_field}")
            
            mapped_record['values'] = mapped_values
            
            # Apply record-level transformation
            if self.record_transform_function:
                try:
                    mapped_record = self.record_transform_function(mapped_record, context)
                except Exception as e:
                    result.add_error(f"Record transformation failed: {e}")
            
            result.mapped_value = mapped_record
            
        except Exception as e:
            result.add_error(f"Record mapping failed: {e}")
        
        return result


class DataMapper:
    """Main data mapper"""
    
    def __init__(self):
        self.model_mappers: Dict[str, ModelMapper] = {}
        self.global_field_mappers: Dict[str, FieldMapper] = {}
        self.global_transform_function: Optional[Callable] = None
        self.logger = get_logger(__name__)
    
    def add_model_mapping(self, source_model: str, target_model: str = None) -> ModelMapper:
        """Add model mapping"""
        mapper = ModelMapper(source_model, target_model)
        self.model_mappers[source_model] = mapper
        return mapper
    
    def add_global_field_mapping(self, source_field: str, target_field: str = None) -> FieldMapper:
        """Add global field mapping (applies to all models)"""
        mapper = FieldMapper(source_field, target_field)
        self.global_field_mappers[source_field] = mapper
        return mapper
    
    def set_global_transform_function(self, transform_func: Callable):
        """Set global transformation function"""
        self.global_transform_function = transform_func
        return self
    
    def map_data(self, data: List[Dict[str, Any]], context: Dict[str, Any] = None) -> List[MappingResult]:
        """Map a list of data records"""
        results = []
        context = context or {}
        
        for i, record in enumerate(data):
            try:
                result = self.map_record(record, context)
                results.append(result)
            except Exception as e:
                error_result = MappingResult(original_value=record)
                error_result.add_error(f"Error mapping record {i}: {e}")
                results.append(error_result)
        
        return results
    
    def map_record(self, record: Dict[str, Any], context: Dict[str, Any] = None) -> MappingResult:
        """Map a single record"""
        result = MappingResult(original_value=record)
        context = context or {}
        
        try:
            model_name = record.get('model')
            if not model_name:
                result.add_error("Record missing model name")
                return result
            
            mapped_record = record.copy()
            
            # Apply model-specific mapping
            if model_name in self.model_mappers:
                model_result = self.model_mappers[model_name].map_record(record, context)
                if model_result.success:
                    mapped_record = model_result.mapped_value
                result.errors.extend(model_result.errors)
                result.warnings.extend(model_result.warnings)
                if not model_result.success:
                    result.success = False
            else:
                # Apply global field mappings
                values = record.get('values', {})
                mapped_values = {}
                
                for field_name, field_value in values.items():
                    if field_name in self.global_field_mappers:
                        field_result = self.global_field_mappers[field_name].map_value(field_value, context)
                        if field_result.success:
                            target_field = self.global_field_mappers[field_name].target_field
                            mapped_values[target_field] = field_result.mapped_value
                        else:
                            mapped_values[field_name] = field_value
                            result.errors.extend(field_result.errors)
                            result.warnings.extend(field_result.warnings)
                    else:
                        mapped_values[field_name] = field_value
                
                mapped_record['values'] = mapped_values
            
            # Apply global transformation
            if self.global_transform_function:
                try:
                    mapped_record = self.global_transform_function(mapped_record, context)
                except Exception as e:
                    result.add_error(f"Global transformation failed: {e}")
            
            result.mapped_value = mapped_record
            
        except Exception as e:
            result.add_error(f"Error mapping record: {e}")
        
        return result
