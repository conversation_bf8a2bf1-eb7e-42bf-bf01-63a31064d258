"""
Workflow Processor for ERP system

Specialized processor for handling workflow-related records like automated actions,
scheduled actions, and workflow transitions.
"""

from typing import Dict, List, Any, Optional

from .base import BaseDataProcessor, ProcessorError
from ..sql_helpers import SQLHelpers, ModelSQLHelpers


class WorkflowProcessor(BaseDataProcessor):
    """
    Processor for workflow records (ir.cron, base.automation, etc.)
    
    Handles creation and updating of workflow-related records with proper
    validation and reference resolution.
    """
    
    def __init__(self, db_manager, name: str = "WorkflowProcessor"):
        super().__init__(db_manager, name)
        
        # Initialize SQL helpers
        self.sql = SQLHelpers(db_manager)
        self.model_sql = ModelSQLHelpers(self.sql)
        
        # Supported workflow models
        self.supported_workflow_models = {
            'ir.cron',
            'base.automation',
            'ir.actions.todo',
            'workflow',
            'workflow.activity',
            'workflow.transition'
        }
    
    def can_process(self, item: Dict[str, Any]) -> bool:
        """Check if this processor can handle the given item"""
        if not isinstance(item, dict):
            return False
        
        model = item.get('model')
        return model in self.supported_workflow_models
    
    def get_supported_models(self) -> List[str]:
        """Get list of models this processor supports"""
        return list(self.supported_workflow_models)
    
    def get_processing_order(self) -> int:
        """Get processing order (process workflows late)"""
        return 80
    
    async def _process_item(self, item: Dict[str, Any], **kwargs) -> bool:
        """Process a single workflow item"""
        model = item['model']
        xml_id = item.get('xml_id')
        values = item.get('values', {})
        noupdate = item.get('noupdate', False)
        
        try:
            # Validate workflow data
            validation_result = await self._validate_workflow_data(values, model)
            if not validation_result['valid']:
                for error in validation_result['errors']:
                    self.result.add_error(f"Workflow validation error for {xml_id}: {error}")
                return False
            
            # Process workflow-specific fields
            processed_values = await self._process_workflow_values(values, model)
            if processed_values is None:
                return False
            
            # Check if record exists
            existing_record_id = None
            if xml_id:
                existing_record_id = await self._find_record_by_xmlid(xml_id)
            
            if existing_record_id:
                # Update existing record
                if not noupdate:
                    success = await self._update_workflow_record(model, existing_record_id, processed_values)
                    if success:
                        self.logger.debug(f"Updated workflow record {xml_id}")
                        return True
                else:
                    self.logger.debug(f"Skipped updating workflow record {xml_id} (noupdate=True)")
                    return True
            else:
                # Create new record
                new_record_id = await self._create_workflow_record(model, processed_values)
                if new_record_id:
                    # Store XML ID mapping
                    if xml_id:
                        await self._store_xmlid_mapping(xml_id, model, new_record_id)
                    
                    self.logger.debug(f"Created workflow record {xml_id or 'no-id'}")
                    return True
            
            return False
            
        except Exception as e:
            error_msg = f"Failed to process workflow record {xml_id or 'no-id'}: {e}"
            self.result.add_error(error_msg)
            return False
    
    async def _validate_workflow_data(self, values: Dict[str, Any], model: str) -> Dict[str, Any]:
        """Validate workflow data structure"""
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        if model == 'ir.cron':
            # Validate scheduled actions
            required_fields = ['name', 'model_id', 'function']
            for field in required_fields:
                if field not in values:
                    result['errors'].append(f"Missing required field for scheduled action: {field}")
                    result['valid'] = False
            
            # Check interval settings
            interval_fields = ['interval_number', 'interval_type']
            for field in interval_fields:
                if field not in values:
                    result['warnings'].append(f"Missing interval field for scheduled action: {field}")
        
        elif model == 'base.automation':
            # Validate automated actions
            required_fields = ['name', 'model_id', 'trigger']
            for field in required_fields:
                if field not in values:
                    result['errors'].append(f"Missing required field for automated action: {field}")
                    result['valid'] = False
            
            # Check trigger-specific requirements
            trigger_def = values.get('trigger', {})
            trigger_value = trigger_def.get('value') if isinstance(trigger_def, dict) else trigger_def
            
            if trigger_value == 'on_create_or_write':
                if 'filter_pre_domain' not in values and 'filter_domain' not in values:
                    result['warnings'].append("No filter domain specified for create/write trigger")
        
        elif model in ['workflow', 'workflow.activity', 'workflow.transition']:
            # Validate workflow components
            if 'name' not in values:
                result['errors'].append(f"Missing required field for {model}: name")
                result['valid'] = False
        
        return result
    
    async def _process_workflow_values(self, values: Dict[str, Any], model: str) -> Optional[Dict[str, Any]]:
        """Process workflow-specific field values"""
        processed = {}
        
        for field_name, field_def in values.items():
            try:
                if field_name == 'model_id':
                    # Special handling for model reference
                    processed[field_name] = await self._process_model_reference(field_def)
                elif field_name in ['user_id', 'group_id']:
                    # Special handling for user/group references
                    processed[field_name] = await self._process_reference_field(field_def)
                elif field_name in ['filter_domain', 'filter_pre_domain']:
                    # Special handling for domain filters
                    processed[field_name] = await self._process_domain_field(field_def)
                elif field_name in ['code', 'python_code']:
                    # Special handling for code fields
                    processed[field_name] = await self._process_code_field(field_def)
                elif field_name in ['interval_number', 'numbercall']:
                    # Special handling for numeric fields
                    processed[field_name] = await self._process_numeric_field(field_def)
                elif field_name in ['active', 'doall']:
                    # Special handling for boolean fields
                    processed[field_name] = await self._process_boolean_field(field_def)
                else:
                    # Standard field processing
                    processed[field_name] = await self._process_standard_field(field_def)
                    
            except Exception as e:
                error_msg = f"Error processing workflow field {field_name}: {e}"
                self.result.add_error(error_msg)
                return None
        
        return processed
    
    async def _process_model_reference(self, field_def: Any) -> Optional[str]:
        """Process model reference"""
        if isinstance(field_def, dict):
            if field_def.get('type') == 'ref':
                ref_value = field_def.get('value')
                return await self._resolve_reference(ref_value)
            else:
                return field_def.get('value')
        else:
            return str(field_def) if field_def else None
    
    async def _process_reference_field(self, field_def: Any) -> Optional[str]:
        """Process reference field"""
        if isinstance(field_def, dict):
            if field_def.get('type') == 'ref':
                ref_value = field_def.get('value')
                return await self._resolve_reference(ref_value)
            else:
                return field_def.get('value')
        else:
            return str(field_def) if field_def else None
    
    async def _process_domain_field(self, field_def: Any) -> str:
        """Process domain field"""
        if isinstance(field_def, dict):
            field_type = field_def.get('type')
            field_value = field_def.get('value', '')
            
            if field_type == 'eval':
                return field_value
            else:
                return str(field_value)
        else:
            return str(field_def) if field_def else ''
    
    async def _process_code_field(self, field_def: Any) -> str:
        """Process code field"""
        if isinstance(field_def, dict):
            field_value = field_def.get('value', '')
            return str(field_value)
        else:
            return str(field_def) if field_def else ''
    
    async def _process_numeric_field(self, field_def: Any) -> int:
        """Process numeric field"""
        if isinstance(field_def, dict):
            field_value = field_def.get('value', '1')
        else:
            field_value = field_def or '1'
        
        try:
            return int(field_value)
        except (ValueError, TypeError):
            return 1
    
    async def _process_boolean_field(self, field_def: Any) -> bool:
        """Process boolean field"""
        if isinstance(field_def, dict):
            field_type = field_def.get('type')
            field_value = field_def.get('value')
            
            if field_type == 'eval':
                return self._evaluate_boolean_expression(field_value)
            else:
                return self._convert_to_boolean(field_value)
        else:
            return self._convert_to_boolean(field_def)
    

    
    def _evaluate_expression(self, expression: str) -> Any:
        """Safely evaluate a Python expression"""
        try:
            if expression == 'True':
                return True
            elif expression == 'False':
                return False
            elif expression == 'None':
                return None
            elif expression.startswith("'") and expression.endswith("'"):
                return expression[1:-1]
            elif expression.startswith('"') and expression.endswith('"'):
                return expression[1:-1]
            else:
                try:
                    return int(expression)
                except ValueError:
                    try:
                        return float(expression)
                    except ValueError:
                        return expression
        except Exception:
            return expression
    
    async def _find_record_by_xmlid(self, xml_id: str) -> Optional[str]:
        """Find a record ID by its XML ID"""
        try:
            return await self.xmlid_manager.resolve_xmlid_to_record_id(xml_id)
        except Exception:
            return None
    
    async def _create_workflow_record(self, model: str, values: Dict[str, Any]) -> Optional[str]:
        """Create a new workflow record"""
        try:
            return await self.model_sql.create_record(model, values)
        except Exception as e:
            self.result.add_error(f"Failed to create workflow record: {e}")
            return None
    
    async def _update_workflow_record(self, model: str, record_id: str, values: Dict[str, Any]) -> bool:
        """Update an existing workflow record"""
        try:
            return await self.model_sql.update_record(model, record_id, values)
        except Exception as e:
            self.result.add_error(f"Failed to update workflow record {record_id}: {e}")
            return False
    
    async def _store_xmlid_mapping(self, xml_id: str, model: str, record_id: str):
        """Store XML ID to record ID mapping"""
        try:
            if '.' in xml_id:
                module, name = xml_id.split('.', 1)
            else:
                module = self.context.get('addon_name', 'base')
                name = xml_id
            
            await self.xmlid_manager.create_xmlid_mapping(
                module=module,
                name=name,
                model=model,
                res_id=record_id
            )
        except Exception as e:
            self.result.add_warning(f"Failed to store XML ID mapping for {xml_id}: {e}")
