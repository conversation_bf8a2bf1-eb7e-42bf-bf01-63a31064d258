"""
Batch Processor for ERP system

Provides batch processing capabilities for handling large data loads
with configurable batch sizes, parallel processing, and progress tracking.
"""

import asyncio
from typing import Dict, List, Any, Optional, Callable, AsyncGenerator
from dataclasses import dataclass, field
from datetime import datetime
import math

from ..processors.manager import Processor<PERSON>anager, ProcessingResult
from ..exceptions import DataLoadingError
from ...logging import get_logger
from ...database.connection.manager import DatabaseManager


@dataclass
class BatchConfig:
    """Configuration for batch processing"""
    batch_size: int = 100
    max_concurrent_batches: int = 3
    enable_progress_tracking: bool = True
    enable_memory_optimization: bool = True
    retry_failed_batches: bool = True
    max_retries: int = 3
    retry_delay: float = 1.0
    checkpoint_interval: int = 10  # Save progress every N batches
    
    def validate(self):
        """Validate configuration"""
        if self.batch_size <= 0:
            raise ValueError("Batch size must be positive")
        if self.max_concurrent_batches <= 0:
            raise ValueError("Max concurrent batches must be positive")
        if self.max_retries < 0:
            raise ValueError("Max retries cannot be negative")


@dataclass
class BatchResult:
    """Result of batch processing"""
    total_batches: int = 0
    completed_batches: int = 0
    failed_batches: int = 0
    total_records: int = 0
    successful_records: int = 0
    failed_records: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    batch_results: List[ProcessingResult] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    checkpoints: List[Dict[str, Any]] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """Calculate overall success rate"""
        if self.total_records == 0:
            return 0.0
        return (self.successful_records / self.total_records) * 100
    
    @property
    def batch_success_rate(self) -> float:
        """Calculate batch success rate"""
        if self.total_batches == 0:
            return 0.0
        return (self.completed_batches / self.total_batches) * 100
    
    def add_batch_result(self, batch_result: ProcessingResult):
        """Add a batch processing result"""
        self.batch_results.append(batch_result)
        self.successful_records += batch_result.successful_items
        self.failed_records += batch_result.failed_items
        self.errors.extend(batch_result.errors)
        self.warnings.extend(batch_result.warnings)
        
        if batch_result.successful_items > 0:
            self.completed_batches += 1
        else:
            self.failed_batches += 1


class BatchProcessor:
    """
    Batch processor for handling large data loads
    
    Provides efficient processing of large datasets by breaking them into
    manageable batches with parallel processing and progress tracking.
    """
    
    def __init__(self, db_manager: DatabaseManager, config: BatchConfig = None):
        """
        Initialize batch processor
        
        Args:
            db_manager: Database manager instance
            config: Batch processing configuration
        """
        self.db_manager = db_manager
        self.config = config or BatchConfig()
        self.config.validate()
        
        self.logger = get_logger(__name__)
        self.processor_manager = ProcessorManager(db_manager)
        
        # Progress tracking
        self.progress_callbacks: List[Callable] = []
        self.checkpoint_callbacks: List[Callable] = []
        
        # State management
        self.is_processing = False
        self.current_batch = 0
        self.total_batches = 0
    
    def add_progress_callback(self, callback: Callable):
        """Add progress tracking callback"""
        self.progress_callbacks.append(callback)
    
    def add_checkpoint_callback(self, callback: Callable):
        """Add checkpoint callback"""
        self.checkpoint_callbacks.append(callback)
    
    async def process_data_batches(self, data: List[Dict[str, Any]], context: Dict[str, Any] = None) -> BatchResult:
        """
        Process data in batches
        
        Args:
            data: List of data records to process
            context: Processing context
            
        Returns:
            BatchResult with comprehensive processing statistics
        """
        if self.is_processing:
            raise DataLoadingError("Batch processor is already running")
        
        self.is_processing = True
        result = BatchResult(
            total_records=len(data),
            start_time=datetime.utcnow()
        )
        
        try:
            self.logger.info(f"Starting batch processing of {len(data)} records")
            
            # Create batches
            batches = self._create_batches(data)
            result.total_batches = len(batches)
            self.total_batches = len(batches)
            
            self.logger.info(f"Created {len(batches)} batches of size {self.config.batch_size}")
            
            # Process batches
            if self.config.max_concurrent_batches > 1:
                await self._process_batches_parallel(batches, result, context)
            else:
                await self._process_batches_sequential(batches, result, context)
            
            self.logger.info(
                f"Batch processing completed: {result.completed_batches}/{result.total_batches} batches successful, "
                f"{result.successful_records}/{result.total_records} records successful"
            )
            
        except Exception as e:
            error_msg = f"Batch processing failed: {e}"
            result.errors.append(error_msg)
            self.logger.error(error_msg)
            
        finally:
            result.end_time = datetime.utcnow()
            if result.start_time:
                result.duration = (result.end_time - result.start_time).total_seconds()
            
            self.is_processing = False
            self.current_batch = 0
            self.total_batches = 0
        
        return result
    
    def _create_batches(self, data: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Create batches from data"""
        batches = []
        batch_size = self.config.batch_size
        
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]
            batches.append(batch)
        
        return batches
    
    async def _process_batches_sequential(self, batches: List[List[Dict[str, Any]]], 
                                        result: BatchResult, context: Dict[str, Any]):
        """Process batches sequentially"""
        for i, batch in enumerate(batches):
            self.current_batch = i + 1
            
            try:
                batch_result = await self._process_single_batch(batch, i + 1, context)
                result.add_batch_result(batch_result)
                
                # Progress callback
                await self._notify_progress(i + 1, len(batches), batch_result)
                
                # Checkpoint
                if (i + 1) % self.config.checkpoint_interval == 0:
                    await self._create_checkpoint(i + 1, result)
                
            except Exception as e:
                error_msg = f"Batch {i + 1} failed: {e}"
                result.errors.append(error_msg)
                result.failed_batches += 1
                self.logger.error(error_msg)
                
                # Retry if configured
                if self.config.retry_failed_batches:
                    retry_result = await self._retry_batch(batch, i + 1, context)
                    if retry_result:
                        result.add_batch_result(retry_result)
    
    async def _process_batches_parallel(self, batches: List[List[Dict[str, Any]]], 
                                      result: BatchResult, context: Dict[str, Any]):
        """Process batches in parallel"""
        semaphore = asyncio.Semaphore(self.config.max_concurrent_batches)
        
        async def process_batch_with_semaphore(batch, batch_num):
            async with semaphore:
                return await self._process_single_batch(batch, batch_num, context)
        
        # Create tasks for all batches
        tasks = [
            asyncio.create_task(process_batch_with_semaphore(batch, i + 1))
            for i, batch in enumerate(batches)
        ]
        
        # Process tasks and collect results
        for i, task in enumerate(asyncio.as_completed(tasks)):
            try:
                batch_result = await task
                result.add_batch_result(batch_result)
                
                self.current_batch = i + 1
                
                # Progress callback
                await self._notify_progress(i + 1, len(batches), batch_result)
                
                # Checkpoint
                if (i + 1) % self.config.checkpoint_interval == 0:
                    await self._create_checkpoint(i + 1, result)
                
            except Exception as e:
                error_msg = f"Parallel batch processing error: {e}"
                result.errors.append(error_msg)
                result.failed_batches += 1
                self.logger.error(error_msg)
    
    async def _process_single_batch(self, batch: List[Dict[str, Any]], batch_num: int, 
                                  context: Dict[str, Any]) -> ProcessingResult:
        """Process a single batch"""
        self.logger.debug(f"Processing batch {batch_num} with {len(batch)} records")
        
        batch_context = context.copy() if context else {}
        batch_context.update({
            'batch_number': batch_num,
            'batch_size': len(batch),
            'is_batch_processing': True
        })
        
        return await self.processor_manager.process_data(batch, batch_context)
    
    async def _retry_batch(self, batch: List[Dict[str, Any]], batch_num: int, 
                         context: Dict[str, Any]) -> Optional[ProcessingResult]:
        """Retry a failed batch"""
        for attempt in range(self.config.max_retries):
            try:
                self.logger.info(f"Retrying batch {batch_num}, attempt {attempt + 1}")
                
                # Wait before retry
                if attempt > 0:
                    await asyncio.sleep(self.config.retry_delay * (attempt + 1))
                
                return await self._process_single_batch(batch, batch_num, context)
                
            except Exception as e:
                self.logger.warning(f"Batch {batch_num} retry {attempt + 1} failed: {e}")
                if attempt == self.config.max_retries - 1:
                    self.logger.error(f"Batch {batch_num} failed after {self.config.max_retries} retries")
        
        return None
    
    async def _notify_progress(self, current_batch: int, total_batches: int, batch_result: ProcessingResult):
        """Notify progress callbacks"""
        if not self.config.enable_progress_tracking:
            return
        
        progress_info = {
            'current_batch': current_batch,
            'total_batches': total_batches,
            'progress_percentage': (current_batch / total_batches) * 100,
            'batch_result': batch_result,
            'timestamp': datetime.utcnow()
        }
        
        for callback in self.progress_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(progress_info)
                else:
                    callback(progress_info)
            except Exception as e:
                self.logger.warning(f"Progress callback failed: {e}")
    
    async def _create_checkpoint(self, current_batch: int, result: BatchResult):
        """Create processing checkpoint"""
        checkpoint = {
            'batch_number': current_batch,
            'timestamp': datetime.utcnow(),
            'completed_batches': result.completed_batches,
            'failed_batches': result.failed_batches,
            'successful_records': result.successful_records,
            'failed_records': result.failed_records,
            'success_rate': result.success_rate
        }
        
        result.checkpoints.append(checkpoint)
        
        for callback in self.checkpoint_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(checkpoint)
                else:
                    callback(checkpoint)
            except Exception as e:
                self.logger.warning(f"Checkpoint callback failed: {e}")
    
    def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status"""
        return {
            'is_processing': self.is_processing,
            'current_batch': self.current_batch,
            'total_batches': self.total_batches,
            'progress_percentage': (self.current_batch / self.total_batches * 100) if self.total_batches > 0 else 0,
            'config': {
                'batch_size': self.config.batch_size,
                'max_concurrent_batches': self.config.max_concurrent_batches,
                'retry_enabled': self.config.retry_failed_batches,
                'max_retries': self.config.max_retries
            }
        }
