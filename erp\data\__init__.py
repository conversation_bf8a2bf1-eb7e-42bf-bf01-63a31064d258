"""
Enhanced Data loading infrastructure for ERP system

This package provides comprehensive XML data file parsing and loading capabilities
with modular processors, validation, transformation, and error handling.
Similar to Odoo's data loading mechanism but with enhanced features.
"""

from .loader import DataLoader
from .parser import XMLDataParser, ParseMode, FieldType
from .exceptions import DataLoadingError, XMLParsingError, ModelNotFoundError, RecordCreationError, XMLIDError
from .xmlid_manager import XMLIDManager
from .sql_helpers import SQLHelpers, ModelSQLHelpers, XMLIDSQLHelpers

# Import processors
from .processors import (
    BaseDataProcessor, ProcessorResult, ProcessorError,
    RecordProcessor, ViewProcessor, MenuProcessor,
    ActionProcessor, SecurityProcessor, WorkflowProcessor
)
from .processors.manager import ProcessorManager, ProcessingResult

# Import validation and transformation utilities
from .validation import (
    DataValidator, FieldValidator, ModelValidator, ValidationResult, ValidationError,
    DataTransformer, FieldTransformer, TypeConverter, TransformationResult, TransformationError
)

__all__ = [
    # Core components
    'DataLoader',
    'XMLDataParser',
    'ParseMode',
    'FieldType',
    'XMLIDManager',

    # SQL helpers
    'SQLHelpers',
    'ModelSQLHelpers',
    'XMLIDSQLHelpers',

    # Processors
    'BaseDataProcessor',
    'ProcessorResult',
    'ProcessorError',
    'RecordProcessor',
    'ViewProcessor',
    'MenuProcessor',
    'ActionProcessor',
    'SecurityProcessor',
    'WorkflowProcessor',
    'ProcessorManager',
    'ProcessingResult',

    # Validation and transformation
    'DataValidator',
    'FieldValidator',
    'ModelValidator',
    'ValidationResult',
    'ValidationError',
    'DataTransformer',
    'FieldTransformer',
    'TypeConverter',
    'TransformationResult',
    'TransformationError',

    # Exceptions
    'DataLoadingError',
    'XMLParsingError',
    'ModelNotFoundError',
    'RecordCreationError',
    'XMLIDError'
]
